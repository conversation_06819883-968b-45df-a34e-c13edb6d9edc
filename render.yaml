services:
  - type: web
    name: citadelle-bot
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: python main.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PORT
        generateValue: true
    healthCheckPath: /health
    autoDeploy: true
    disk:
      name: citadelle-data
      mountPath: /opt/render/project/data
      sizeGB: 1
